

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PierWater International, LLC</title>
    <meta name="description" content="PierWater International, LLC (PWI) is a Service-Disabled, Veteran-Owned, Small Business focused on high-impact investments in FinTech, Medical, Real Estate, and Community Development.">
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <script src="https://unpkg.com/@heroicons/v1/outline"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#0C2D48',
                        accent: '#00A6A6',
                        gold: '#D4AF37',
                    },
                    fontFamily: {
                        sans: ['Inter', 'sans-serif'],
                    },
                }
            }
        }
    </script>
    <style>
        .hero-bg {
            background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='100%25' height='100%25'%3E%3Cdefs%3E%3ClinearGradient id='a' x1='0' x2='0' y1='0' y2='1'%3E%3Cstop offset='0' stop-color='%230C2D48'/%3E%3Cstop offset='1' stop-color='%2300A6A6'/%3E%3C/linearGradient%3E%3C/defs%3E%3Cpattern id='b' width='24' height='24' patternUnits='userSpaceOnUse'%3E%3Ccircle fill='%23ffffff' cx='12' cy='12' r='0.5'/%3E%3C/pattern%3E%3Crect width='100%25' height='100%25' fill='url(%23a)'/%3E%3Crect width='100%25' height='100%25' fill='url(%23b)' fill-opacity='0.1'/%3E%3C/svg%3E");
            background-size: cover;
        }
        
        .ripple {
            position: absolute;
            border-radius: 50%;
            background-color: rgba(255, 255, 255, 0.1);
            transform: scale(0);
            animation: ripple 4s linear infinite;
        }
        
        @keyframes ripple {
            0% {
                transform: scale(0);
                opacity: 0.5;
            }
            100% {
                transform: scale(1);
                opacity: 0;
            }
        }
        
        .globe {
            position: relative;
            width: 200px;
            height: 200px;
            border-radius: 50%;
            background: radial-gradient(circle at 70% 30%, #ffffff 0%, #0C2D48 60%);
            box-shadow: 0 0 20px rgba(0, 166, 166, 0.5);
            overflow: hidden;
        }
        
        .globe::before {
            content: '';
            position: absolute;
            width: 210px;
            height: 210px;
            border-radius: 50%;
            background: linear-gradient(90deg, transparent 50%, rgba(0, 166, 166, 0.2) 50%);
            animation: rotate 8s linear infinite;
        }
        
        .globe::after {
            content: '';
            position: absolute;
            width: 210px;
            height: 210px;
            border-radius: 50%;
            background: linear-gradient(transparent 50%, rgba(0, 166, 166, 0.1) 50%);
            animation: rotate 12s linear infinite;
        }
        
        @keyframes rotate {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .service-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1);
        }
        
        .team-card:hover img {
            transform: scale(1.05);
        }
    </style>
</head>
<body class="font-sans text-gray-800 bg-gray-50">
    <!-- Navigation -->
    <nav class="bg-primary text-white shadow-lg sticky top-0 z-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <div class="flex-shrink-0 flex items-center">
                        <span class="text-2xl font-bold">PierWater</span>
                        <span class="ml-1 text-accent font-light">International</span>
                    </div>
                </div>
                <div class="hidden md:flex items-center space-x-8">
                    <a href="#home" class="hover:text-accent transition-colors">Home</a>
                    <a href="#about" class="hover:text-accent transition-colors">About Us</a>
                    <a href="#services" class="hover:text-accent transition-colors">Services</a>
                    <a href="#portfolio" class="hover:text-accent transition-colors">Portfolio</a>
                    <a href="#team" class="hover:text-accent transition-colors">Team</a>
                    <a href="#contact" class="hover:text-accent transition-colors">Contact</a>
                    <a href="#investor" class="bg-accent hover:bg-opacity-80 px-4 py-2 rounded-md transition-colors">Investor Login</a>
                </div>
                <div class="md:hidden flex items-center">
                    <button id="mobile-menu-button" class="text-white hover:text-accent">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
                        </svg>
                    </button>
                </div>
            </div>
        </div>
        
        <!-- Mobile menu -->
        <div id="mobile-menu" class="hidden md:hidden bg-primary pb-4 px-4">
            <div class="space-y-1">
                <a href="#home" class="block py-2 hover:text-accent transition-colors">Home</a>
                <a href="#about" class="block py-2 hover:text-accent transition-colors">About Us</a>
                <a href="#services" class="block py-2 hover:text-accent transition-colors">Services</a>
                <a href="#portfolio" class="block py-2 hover:text-accent transition-colors">Portfolio</a>
                <a href="#team" class="block py-2 hover:text-accent transition-colors">Team</a>
                <a href="#contact" class="block py-2 hover:text-accent transition-colors">Contact</a>
                <a href="#investor" class="block py-2 bg-accent hover:bg-opacity-80 px-4 rounded-md transition-colors mt-4">Investor Login</a>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section id="home" class="hero-bg relative overflow-hidden">
        <div class="ripple" style="width: 300px; height: 300px; left: 20%; top: 30%;"></div>
        <div class="ripple" style="width: 400px; height: 400px; left: 60%; top: 60%; animation-delay: 1s;"></div>
        <div class="ripple" style="width: 500px; height: 500px; left: 40%; top: 40%; animation-delay: 2s;"></div>
        
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24 md:py-32">
            <div class="flex flex-col md:flex-row items-center">
                <div class="md:w-1/2 text-white">
                    <div class="inline-block bg-accent px-4 py-1 rounded-full text-sm font-semibold mb-6">
                        Service-Disabled, Veteran-Owned Small Business
                    </div>
                    <h1 class="text-4xl md:text-5xl font-bold leading-tight mb-6">
                        Investing in Innovation, <br>
                        <span class="text-accent">Empowering Communities</span>
                    </h1>
                    <p class="text-lg mb-8 text-gray-200 max-w-lg">
                        PierWater International focuses on high-impact investments in FinTech, Medical, Real Estate, and Community Development.
                    </p>
                    <div class="flex flex-wrap gap-4">
                        <a href="#services" class="bg-accent hover:bg-opacity-80 text-white px-6 py-3 rounded-md font-medium transition-colors">
                            Learn About Our Work
                        </a>
                        <a href="#contact" class="border border-white hover:bg-white hover:text-primary text-white px-6 py-3 rounded-md font-medium transition-colors">
                            Connect With Us
                        </a>
                    </div>
                </div>
                <div class="md:w-1/2 flex justify-center mt-12 md:mt-0">
                    <div class="globe"></div>
                </div>
            </div>
        </div>
    </section>

    <!-- About Section -->
    <section id="about" class="py-20 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="text-3xl md:text-4xl font-bold text-primary mb-4">About PierWater International</h2>
                <div class="w-24 h-1 bg-accent mx-auto"></div>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-12 items-center">
                <div>
                    <h3 class="text-2xl font-semibold mb-4 text-primary">Who We Are</h3>
                    <p class="text-gray-600 mb-6">
                        PierWater International, LLC (PWI) is a Service-Disabled, Veteran-Owned, Small Business (SDVOSB) 
                        dedicated to creating positive impact through strategic investments in technology, real estate, 
                        and community development initiatives.
                    </p>
                    
                    <h3 class="text-2xl font-semibold mb-4 text-primary">Our Mission</h3>
                    <p class="text-gray-600 mb-6">
                        To drive innovation and economic growth while empowering underserved communities through 
                        responsible investment strategies and sustainable development practices.
                    </p>
                    
                    <h3 class="text-2xl font-semibold mb-4 text-primary">Our Vision</h3>
                    <p class="text-gray-600 mb-6">
                        Creating a world where technological advancement and community development go hand in hand, 
                        building a more equitable and sustainable future for all.
                    </p>
                    
                    <div class="flex items-center space-x-4 mt-8">
                        <div class="bg-primary text-white p-3 rounded-full">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                        </div>
                        <div>
                            <h4 class="font-semibold">SDVOSB Certified</h4>
                            <p class="text-sm text-gray-500">Service-Disabled Veteran-Owned Small Business</p>
                        </div>
                    </div>
                </div>
                
                <div class="bg-gray-100 p-8 rounded-lg">
                    <div class="flex items-center mb-6">
                        <img src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='80' height='80' viewBox='0 0 24 24' fill='none' stroke='%230C2D48' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2'%3E%3C/path%3E%3Ccircle cx='12' cy='7' r='4'%3E%3C/circle%3E%3C/svg%3E" 
                             alt="Founder" class="w-20 h-20 rounded-full bg-white p-2 mr-4">
                        <div>
                            <h3 class="text-xl font-semibold">Joel Rodriguez</h3>
                            <p class="text-accent">Founder & CEO</p>
                        </div>
                    </div>
                    <p class="text-gray-600 mb-6">
                        A service-disabled veteran with extensive experience in finance, technology, and community development. 
                        Joel founded PierWater International with a vision to bridge the gap between innovative technology 
                        and community empowerment.
                    </p>
                    
                    <h4 class="font-semibold text-primary mb-3">Impact Highlights</h4>
                    <div class="grid grid-cols-2 gap-4">
                        <div class="bg-white p-4 rounded shadow-sm">
                            <div class="text-2xl font-bold text-accent">$25M+</div>
                            <div class="text-sm text-gray-500">Investments Managed</div>
                        </div>
                        <div class="bg-white p-4 rounded shadow-sm">
                            <div class="text-2xl font-bold text-accent">12+</div>
                            <div class="text-sm text-gray-500">Community Projects</div>
                        </div>
                        <div class="bg-white p-4 rounded shadow-sm">
                            <div class="text-2xl font-bold text-accent">5+</div>
                            <div class="text-sm text-gray-500">Tech Startups Funded</div>
                        </div>
                        <div class="bg-white p-4 rounded shadow-sm">
                            <div class="text-2xl font-bold text-accent">3</div>
                            <div class="text-sm text-gray-500">States with Active Projects</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Services Section -->
    <section id="services" class="py-20 bg-gray-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="text-3xl md:text-4xl font-bold text-primary mb-4">Core Services</h2>
                <div class="w-24 h-1 bg-accent mx-auto mb-6"></div>
                <p class="max-w-2xl mx-auto text-gray-600">
                    Our comprehensive approach to investment and development spans multiple sectors, 
                    creating value and driving positive change.
                </p>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                <!-- Technology Development -->
                <div class="bg-white rounded-lg shadow-md overflow-hidden service-card transition-all duration-300">
                    <div class="bg-primary h-2"></div>
                    <div class="p-6">
                        <div class="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mb-4">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                            </svg>
                        </div>
                        <h3 class="text-xl font-semibold mb-3 text-primary">Technology Development</h3>
                        <ul class="space-y-2 text-gray-600 mb-6">
                            <li class="flex items-center">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-accent mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                </svg>
                                FinTech Solutions
                            </li>
                            <li class="flex items-center">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-accent mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                </svg>
                                Medical Devices
                            </li>
                            <li class="flex items-center">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-accent mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                </svg>
                                AI Applications
                            </li>
                            <li class="flex items-center">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-accent mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                </svg>
                                Clean Tech
                            </li>
                        </ul>
                        <a href="#contact" class="text-accent hover:text-primary font-medium flex items-center">
                            Learn more
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                            </svg>
                        </a>
                    </div>
                </div>
                
                <!-- Investment Strategy -->
                <div class="bg-white rounded-lg shadow-md overflow-hidden service-card transition-all duration-300">
                    <div class="bg-accent h-2"></div>
                    <div class="p-6">
                        <div class="w-12 h-12 bg-accent/10 rounded-lg flex items-center justify-center mb-4">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-accent" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                        </div>
                        <h3 class="text-xl font-semibold mb-3 text-primary">Investment Strategy</h3>
                        <ul class="space-y-2 text-gray-600 mb-6">
                            <li class="flex items-center">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-accent mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                </svg>
                                DPMP (Debt, Preferred, Mezzanine, Private)
                            </li>
                            <li class="flex items-center">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-accent mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                </svg>
                                Opportunity Zones
                            </li>
                            <li class="flex items-center">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-accent mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                </svg>
                                Native Lands Development
                            </li>
                            <li class="flex items-center">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-accent mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                </svg>
                                Strategic Partnerships
                            </li>
                        </ul>
                        <a href="#contact" class="text-accent hover:text-primary font-medium flex items-center">
                            Learn more
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                            </svg>
                        </a>
                    </div>
                </div>
                
                <!-- Community Impact -->
                <div class="bg-white rounded-lg shadow-md overflow-hidden service-card transition-all duration-300">
                    <div class="bg-gold h-2"></div>
                    <div class="p-6">
                        <div class="w-12 h-12 bg-gold/10 rounded-lg flex items-center justify-center mb-4">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-gold" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                            </svg>
                        </div>
                        <h3 class="text-xl font-semibold mb-3 text-primary">Community Impact</h3>
                        <ul class="space-y-2 text-gray-600 mb-6">
                            <li class="flex items-center">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-accent mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                </svg>
                                Affordable Housing
                            </li>
                            <li class="flex items-center">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-accent mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                </svg>
                                Education Initiatives
                            </li>
                            <li class="flex items-center">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-accent mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                </svg>
                                Financial Literacy Programs
                            </li>
                            <li class="flex items-center">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-accent mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                </svg>
                                Infrastructure Development
                            </li>
                        </ul>
                        <a href="#contact" class="text-accent hover:text-primary font-medium flex items-center">
                            Learn more
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                            </svg>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Portfolio Section -->
    <section id="portfolio" class="py-20 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="text-3xl md:text-4xl font-bold text-primary mb-4">Focus Areas</h2>
                <div class="w-24 h-1 bg-accent mx-auto mb-6"></div>
                <p class="max-w-2xl mx-auto text-gray-600">
                    Our diverse portfolio spans multiple sectors, with a focus on innovation and community impact.
                </p>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                <!-- FinTech -->
                <div class="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow duration-300">
                    <div class="h-48 bg-primary/10 flex items-center justify-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-20 w-20 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                    </div>
                    <div class="p-6">
                        <h3 class="text-xl font-semibold mb-2 text-primary">FinTech Investments</h3>
                        <p class="text-gray-600 mb-4">
                            Supporting innovative financial technology solutions that improve access to financial services and promote financial inclusion.
                        </p>
                        <div class="flex flex-wrap gap-2">
                            <span class="bg-primary/10 text-primary text-xs px-2 py-1 rounded">Digital Banking</span>
                            <span class="bg-primary/10 text-primary text-xs px-2 py-1 rounded">Blockchain</span>
                            <span class="bg-primary/10 text-primary text-xs px-2 py-1 rounded">Payment Solutions</span>
                        </div>
                    </div>
                </div>
                
                <!-- Real Estate -->
                <div class="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow duration-300">
                    <div class="h-48 bg-accent/10 flex items-center justify-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-20 w-20 text-accent" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                        </svg>
                    </div>
                    <div class="p-6">
                        <h3 class="text-xl font-semibold mb-2 text-primary">Real Estate Projects</h3>
                        <p class="text-gray-600 mb-4">
                            Developing sustainable real estate projects that address housing needs and create economic opportunities in underserved communities.
                        </p>
                        <div class="flex flex-wrap gap-2">
                            <span class="bg-accent/10 text-accent text-xs px-2 py-1 rounded">Affordable Housing</span>
                            <span class="bg-accent/10 text-accent text-xs px-2 py-1 rounded">Mixed-Use Development</span>
                            <span class="bg-accent/10 text-accent text-xs px-2 py-1 rounded">Opportunity Zones</span>
                        </div>
                    </div>
                </div>
                
                <!-- Clean Water -->
                <div class="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow duration-300">
                    <div class="h-48 bg-blue-100 flex items-center justify-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-20 w-20 text-blue-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z" />
                        </svg>
                    </div>
                    <div class="p-6">
                        <h3 class="text-xl font-semibold mb-2 text-primary">Clean Water / Infrastructure</h3>
                        <p class="text-gray-600 mb-4">
                            Investing in clean water solutions and infrastructure projects that improve quality of life and environmental sustainability.
                        </p>
                        <div class="flex flex-wrap gap-2">
                            <span class="bg-blue-100 text-blue-500 text-xs px-2 py-1 rounded">Water Purification</span>
                            <span class="bg-blue-100 text-blue-500 text-xs px-2 py-1 rounded">Sustainable Infrastructure</span>
                            <span class="bg-blue-100 text-blue-500 text-xs px-2 py-1 rounded">Community Access</span>
                        </div>
                    </div>
                </div>
                
                <!-- Medical Devices -->
                <div class="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow duration-300">
                    <div class="h-48 bg-red-100 flex items-center justify-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-20 w-20 text-red-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                        </svg>
                    </div>
                    <div class="p-6">
                        <h3 class="text-xl font-semibold mb-2 text-primary">Medical Devices</h3>
                        <p class="text-gray-600 mb-4">
                            Supporting the development of innovative medical devices that improve healthcare outcomes and accessibility.
                        </p>
                        <div class="flex flex-wrap gap-2">
                            <span class="bg-red-100 text-red-500 text-xs px-2 py-1 rounded">Diagnostic Tools</span>
                            <span class="bg-red-100 text-red-500 text-xs px-2 py-1 rounded">Remote Monitoring</span>
                            <span class="bg-red-100 text-red-500 text-xs px-2 py-1 rounded">Assistive Technology</span>
                        </div>
                    </div>
                </div>
                
                <!-- Community Capital -->
                <div class="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow duration-300">
                    <div class="h-48 bg-green-100 flex items-center justify-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-20 w-20 text-green-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                        </svg>
                    </div>
                    <div class="p-6">
                        <h3 class="text-xl font-semibold mb-2 text-primary">Community Capital Funds</h3>
                        <p class="text-gray-600 mb-4">
                            Creating and managing capital funds that support community development and entrepreneurship in underserved areas.
                        </p>
                        <div class="flex flex-wrap gap-2">
                            <span class="bg-green-100 text-green-500 text-xs px-2 py-1 rounded">Microfinance</span>
                            <span class="bg-green-100 text-green-500 text-xs px-2 py-1 rounded">Small Business Support</span>
                            <span class="bg-green-100 text-green-500 text-xs px-2 py-1 rounded">Education Funding</span>
                        </div>
                    </div>
                </div>
                
                <!-- Native Lands -->
                <div class="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow duration-300">
                    <div class="h-48 bg-yellow-100 flex items-center justify-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-20 w-20 text-yellow-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                    </div>
                    <div class="p-6">
                        <h3 class="text-xl font-semibold mb-2 text-primary">Native Lands Development</h3>
                        <p class="text-gray-600 mb-4">
                            Partnering with indigenous communities to develop sustainable economic opportunities while respecting cultural heritage.
                        </p>
                        <div class="flex flex-wrap gap-2">
                            <span class="bg-yellow-100 text-yellow-600 text-xs px-2 py-1 rounded">Sustainable Tourism</span>
                            <span class="bg-yellow-100 text-yellow-600 text-xs px-2 py-1 rounded">Cultural Preservation</span>
                            <span class="bg-yellow-100 text-yellow-600 text-xs px-2 py-1 rounded">Economic Development</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Team Section -->
    <section id="team" class="py-20 bg-gray-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="text-3xl md:text-4xl font-bold text-primary mb-4">Our Leadership Team</h2>
                <div class="w-24 h-1 bg-accent mx-auto mb-6"></div>
                <p class="max-w-2xl mx-auto text-gray-600">
                    Meet the experienced professionals leading PierWater International's mission to drive innovation and community impact.
                </p>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                <!-- Joel Rodriguez -->
                <div class="bg-white rounded-lg shadow-md overflow-hidden team-card">
                    <div class="h-64 bg-gray-200 overflow-hidden">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-full w-full text-gray-400 transition-transform duration-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                        </svg>
                    </div>
                    <div class="p-6">
                        <h3 class="text-xl font-semibold text-primary">Joel Rodriguez</h3>
                        <p class="text-accent mb-3">CEO & Founder</p>
                        <p class="text-gray-600 mb-4">
                            Service-disabled veteran with extensive experience in finance and technology. 
                            Joel founded PierWater International with a vision to bridge innovation and community empowerment.
                        </p>
                        <div class="flex space-x-3">
                            <a href="#" class="text-gray-400 hover:text-primary">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M19 0h-14c-2.761 0-5 2.239-5 5v14c0 2.761 2.239 5 5 5h14c2.762 0 5-2.239 5-5v-14c0-2.761-2.238-5-5-5zm-11 19h-3v-11h3v11zm-1.5-12.268c-.966 0-1.75-.79-1.75-1.764s.784-1.764 1.75-1.764 1.75.79 1.75 1.764-.783 1.764-1.75 1.764zm13.5 12.268h-3v-5.604c0-3.368-4-3.113-4 0v5.604h-3v-11h3v1.765c1.396-2.586 7-2.777 7 2.476v6.759z"/>
                                </svg>
                            </a>
                            <a href="#" class="text-gray-400 hover:text-primary">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z"/>
                                </svg>
                            </a>
                        </div>
                    </div>
                </div>
                
                <!-- Carlos Badillo -->
                <div class="bg-white rounded-lg shadow-md overflow-hidden team-card">
                    <div class="h-64 bg-gray-200 overflow-hidden">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-full w-full text-gray-400 transition-transform duration-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                        </svg>
                    </div>
                    <div class="p-6">
                        <h3 class="text-xl font-semibold text-primary">Carlos Badillo</h3>
                        <p class="text-accent mb-3">President</p>
                        <p class="text-gray-600 mb-4">
                            With over 15 years of experience in strategic business development and community investment, 
                            Carlos leads our operational initiatives and partnership development.
                        </p>
                        <div class="flex space-x-3">
                            <a href="#" class="text-gray-400 hover:text-primary">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M19 0h-14c-2.761 0-5 2.239-5 5v14c0 2.761 2.239 5 5 5h14c2.762 0 5-2.239 5-5v-14c0-2.761-2.238-5-5-5zm-11 19h-3v-11h3v11zm-1.5-12.268c-.966 0-1.75-.79-1.75-1.764s.784-1.764 1.75-1.764 1.75.79 1.75 1.764-.783 1.764-1.75 1.764zm13.5 12.268h-3v-5.604c0-3.368-4-3.113-4 0v5.604h-3v-11h3v1.765c1.396-2.586 7-2.777 7 2.476v6.759z"/>
                                </svg>
                            </a>
                            <a href="#" class="text-gray-400 hover:text-primary">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z"/>
                                </svg>
                            </a>
                        </div>
                    </div>
                </div>
                
                <!-- David Kratochvil -->
                <div class="bg-white rounded-lg shadow-md overflow-hidden team-card">
                    <div class="h-64 bg-gray-200 overflow-hidden">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-full w-full text-gray-400 transition-transform duration-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                        </svg>
                    </div>
                    <div class="p-6">
                        <h3 class="text-xl font-semibold text-primary">David Kratochvil</h3>
                        <p class="text-accent mb-3">Chief Financial Officer</p>
                        <p class="text-gray-600 mb-4">
                            Financial expert with a background in investment banking and venture capital. 
                            David oversees our financial strategy and investment portfolio management.
                        </p>
                        <div class="flex space-x-3">
                            <a href="#" class="text-gray-400 hover:text-primary">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M19 0h-14c-2.761 0-5 2.239-5 5v14c0 2.761 2.239 5 5 5h14c2.762 0 5-2.239 5-5v-14c0-2.761-2.238-5-5-5zm-11 19h-3v-11h3v11zm-1.5-12.268c-.966 0-1.75-.79-1.75-1.764s.784-1.764 1.75-1.764 1.75.79 1.75 1.764-.783 1.764-1.75 1.764zm13.5 12.268h-3v-5.604c0-3.368-4-3.113-4 0v5.604h-3v-11h3v1.765c1.396-2.586 7-2.777 7 2.476v6.759z"/>
                                </svg>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Contact Section -->
    <section id="contact" class="py-20 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="text-3xl md:text-4xl font-bold text-primary mb-4">Connect With Us</h2>
                <div class="w-24 h-1 bg-accent mx-auto mb-6"></div>
                <p class="max-w-2xl mx-auto text-gray-600">
                    Have questions about our services or interested in partnering with us? 
                    We'd love to hear from you.
                </p>
            </div>
            
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-12">
                <div>
                    <form id="contact-form" class="space-y-6">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label for="name" class="block text-sm font-medium text-gray-700 mb-1">Full Name</label>
                                <input type="text" id="name" name="name" class="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-accent focus:border-accent">
                            </div>
                            <div>
                                <label for="email" class="block text-sm font-medium text-gray-700 mb-1">Email Address</label>
                                <input type="email" id="email" name="email" class="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-accent focus:border-accent">
                            </div>
                        </div>
                        
                        <div>
                            <label for="subject" class="block text-sm font-medium text-gray-700 mb-1">Subject</label>
                            <input type="text" id="subject" name="subject" class="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-accent focus:border-accent">
                        </div>
                        
                        <div>
                            <label for="message" class="block text-sm font-medium text-gray-700 mb-1">Message</label>
                            <textarea id="message" name="message" rows="5" class="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-accent focus:border-accent"></textarea>
                        </div>
                        
                        <div>
                            <button type="submit" class="w-full bg-primary hover:bg-primary/90 text-white font-medium py-3 px-6 rounded-md transition-colors">
                                Send Message
                            </button>
                            <p class="text-sm text-gray-500 mt-2 text-center">
                                This is a demo form. In a live implementation, this would be connected to a secure backend.
                            </p>
                        </div>
                    </form>
                </div>
                
                <div>
                    <div class="bg-gray-50 p-8 rounded-lg shadow-sm mb-8">
                        <h3 class="text-xl font-semibold text-primary mb-4">Contact Information</h3>
                        
                        <div class="space-y-4">
                            <div class="flex items-start">
                                <div class="flex-shrink-0 h-6 w-6 text-accent mr-3">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                                    </svg>
                                </div>
                                <div>
                                    <p class="font-medium">Office Location</p>
                                    <p class="text-gray-600">1929 Baltimore Annapolis Blvd, Annapolis, MD 21409</p>
                                </div>
                            </div>
                            
                            <div class="flex items-start">
                                <div class="flex-shrink-0 h-6 w-6 text-accent mr-3">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                                    </svg>
                                </div>
                                <div>
                                    <p class="font-medium">Email</p>
                                    <p class="text-gray-600"><EMAIL></p>
                                </div>
                            </div>
                            
                            <div class="flex items-start">
                                <div class="flex-shrink-0 h-6 w-6 text-accent mr-3">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z" />
                                    </svg>
                                </div>
                                <div>
                                    <p class="font-medium">Phone</p>
                                    <p class="text-gray-600">(*************</p>
                                </div>
                            </div>
                            
                            <div class="flex items-start">
                                <div class="flex-shrink-0 h-6 w-6 text-accent mr-3">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                    </svg>
                                </div>
                                <div>
                                    <p class="font-medium">EIN</p>
                                    <p class="text-gray-600">27-0826311</p>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mt-6">
                            <h4 class="font-medium mb-2">Connect With Us</h4>
                            <div class="flex space-x-4">
                                <a href="#" class="text-gray-400 hover:text-primary">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="currentColor" viewBox="0 0 24 24">
                                        <path d="M19 0h-14c-2.761 0-5 2.239-5 5v14c0 2.761 2.239 5 5 5h14c2.762 0 5-2.239 5-5v-14c0-2.761-2.238-5-5-5zm-11 19h-3v-11h3v11zm-1.5-12.268c-.966 0-1.75-.79-1.75-1.764s.784-1.764 1.75-1.764 1.75.79 1.75 1.764-.783 1.764-1.75 1.764zm13.5 12.268h-3v-5.604c0-3.368-4-3.113-4 0v5.604h-3v-11h3v1.765c1.396-2.586 7-2.777 7 2.476v6.759z"/>
                                    </svg>
                                </a>
                                <a href="#" class="text-gray-400 hover:text-primary">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="currentColor" viewBox="0 0 24 24">
                                        <path d="M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z"/>
                                    </svg>
                                </a>
                            </div>
                        </div>
                    </div>
                    
                    <div class="h-64 bg-gray-300 rounded-lg overflow-hidden">
                        <!-- Embedded map placeholder -->
                        <div class="w-full h-full flex items-center justify-center bg-gray-200">
                            <div class="text-center">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 text-gray-400 mx-auto mb-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                                </svg>
                                <p class="text-gray-500">Google Maps would be embedded here</p>
                                <p class="text-sm text-gray-400">1929 Baltimore Annapolis Blvd, Annapolis, MD 21409</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Investor Section -->
    <section id="investor" class="py-20 bg-gray-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="text-3xl md:text-4xl font-bold text-primary mb-4">Investor Relations</h2>
                <div class="w-24 h-1 bg-accent mx-auto mb-6"></div>
                <p class="max-w-2xl mx-auto text-gray-600">
                    Access secure investor information and resources.
                </p>
            </div>
            
            <div class="max-w-md mx-auto bg-white rounded-lg shadow-md overflow-hidden">
                <div class="p-8">
                    <div class="text-center mb-6">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 text-primary mx-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                        </svg>
                        <h3 class="text-xl font-semibold mt-2">Investor Login</h3>
                        <p class="text-gray-500 text-sm mt-1">Access your secure investor dashboard</p>
                    </div>
                    
                    <form id="login-form" class="space-y-4">
                        <div>
                            <label for="investor-email" class="block text-sm font-medium text-gray-700 mb-1">Email</label>
                            <input type="email" id="investor-email" name="email" class="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-accent focus:border-accent">
                        </div>
                        
                        <div>
                            <div class="flex items-center justify-between mb-1">
                                <label for="investor-password" class="block text-sm font-medium text-gray-700">Password</label>
                                <a href="#" class="text-xs text-accent hover:text-primary">Forgot password?</a>
                            </div>
                            <input type="password" id="investor-password" name="password" class="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-accent focus:border-accent">
                        </div>
                        
                        <div>
                            <button type="submit" class="w-full bg-primary hover:bg-primary/90 text-white font-medium py-2 px-4 rounded-md transition-colors">
                                Sign In
                            </button>
                        </div>
                    </form>
                    
                    <div class="mt-6 text-center">
                        <p class="text-sm text-gray-500">
                            Not an investor yet? <a href="#contact" class="text-accent hover:text-primary">Contact us</a> to learn more.
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-primary text-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                <div class="md:col-span-1">
                    <div class="flex items-center mb-4">
                        <span class="text-2xl font-bold">PierWater</span>
                        <span class="ml-1 text-accent font-light">International</span>
                    </div>
                    <p class="text-gray-300 text-sm mb-4">
                        Investing in Innovation, Empowering Communities
                    </p>
                    <div class="flex space-x-4">
                        <a href="#" class="text-gray-300 hover:text-white">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M19 0h-14c-2.761 0-5 2.239-5 5v14c0 2.761 2.239 5 5 5h14c2.762 0 5-2.239 5-5v-14c0-2.761-2.238-5-5-5zm-11 19h-3v-11h3v11zm-1.5-12.268c-.966 0-1.75-.79-1.75-1.764s.784-1.764 1.75-1.764 1.75.79 1.75 1.764-.783 1.764-1.75 1.764zm13.5 12.268h-3v-5.604c0-3.368-4-3.113-4 0v5.604h-3v-11h3v1.765c1.396-2.586 7-2.777 7 2.476v6.759z"/>
                            </svg>
                        </a>
                        <a href="#" class="text-gray-300 hover:text-white">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z"/>
                            </svg>
                        </a>
                    </div>
                </div>
                
                <div>
                    <h3 class="text-lg font-semibold mb-4">Quick Links</h3>
                    <ul class="space-y-2">
                        <li><a href="#home" class="text-gray-300 hover:text-white">Home</a></li>
                        <li><a href="#about" class="text-gray-300 hover:text-white">About Us</a></li>
                        <li><a href="#services" class="text-gray-300 hover:text-white">Services</a></li>
                        <li><a href="#portfolio" class="text-gray-300 hover:text-white">Portfolio</a></li>
                        <li><a href="#team" class="text-gray-300 hover:text-white">Team</a></li>
                        <li><a href="#contact" class="text-gray-300 hover:text-white">Contact</a></li>
                    </ul>
                </div>
                
                <div>
                    <h3 class="text-lg font-semibold mb-4">Focus Areas</h3>
                    <ul class="space-y-2">
                        <li><a href="#portfolio" class="text-gray-300 hover:text-white">FinTech</a></li>
                        <li><a href="#portfolio" class="text-gray-300 hover:text-white">Real Estate</a></li>
                        <li><a href="#portfolio" class="text-gray-300 hover:text-white">Medical Devices</a></li>
                        <li><a href="#portfolio" class="text-gray-300 hover:text-white">Clean Water</a></li>
                        <li><a href="#portfolio" class="text-gray-300 hover:text-white">Community Capital</a></li>
                    </ul>
                </div>
                
                <div>
                    <h3 class="text-lg font-semibold mb-4">Contact</h3>
                    <ul class="space-y-2 text-gray-300">
                        <li class="flex items-start">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                            </svg>
                            <script>(function(){function c(){var b=a.contentDocument||a.contentWindow.document;if(b){var d=b.createElement('script');d.innerHTML="window.__CF$cv$params={r:'958948ac63dfa052',t:'MTc1MTQwODM1NS4wMDAwMDA='};var a=document.createElement('script');a.nonce='';a.src='/cdn-cgi/challenge-platform/scripts/jsd/main.js';document.getElementsByTagName('head')[0].appendChild(a);";b.getElementsByTagName('head')[0].appendChild(d)}}if(document.body){var a=document.createElement('iframe');a.height=1;a.width=1;a.style.position='absolute';a.style.top=0;a.style.left=0;a.style.border='none';a.style.visibility='hidden';document.body.appendChild(a);if('loading'!==document.readyState)c();else if(window.addEventListener)document.addEventListener('DOMContentLoaded',c);else{var e=document.onreadystatechange||function(){};document.onreadystatechange=function(b){e(b);'loading'!==document.readyState&&(document.onreadystatechange=e,c())}}}})();</script>